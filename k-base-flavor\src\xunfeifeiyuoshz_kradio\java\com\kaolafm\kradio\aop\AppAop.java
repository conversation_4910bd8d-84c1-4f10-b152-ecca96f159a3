package com.kaolafm.kradio.aop;

import android.util.Log;


import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
public class AppAop {
    private static final String TAG = "AppAop";

    @Around("execution(* com.kaolafm.kradio.lib.base.AppManager.killAll(..))")
    public void AppManager_killAll(ProceedingJoinPoint point) throws Throwable {
        Log.d(TAG, "AppManager_killAll");
        // 始终只执行原始 killAll（finish Activity），不再在切面中做进程级别的处置
        try {
            point.proceed();
        } finally {
            if (AppManager.getInstance().getCurrentActivity() == null) {
                // 仅记录信息，避免无 Activity 场景下的自杀式退出
                android.util.Log.i("AppAop", "killAll proceeded with no currentActivity; skipping killProcess/System.exit by design");
            }
        }
    }
}
