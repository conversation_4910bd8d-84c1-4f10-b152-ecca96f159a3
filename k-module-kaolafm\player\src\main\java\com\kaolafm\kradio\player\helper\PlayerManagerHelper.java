package com.kaolafm.kradio.player.helper;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Process;

import androidx.annotation.NonNull;

import android.telephony.TelephonyManager;
import android.util.Log;

import com.google.gson.Gson;
import com.kaolafm.ad.api.model.AudioAdvert;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.lib.bean.SubscribeData;
import com.kaolafm.kradio.lib.bean.AdPlayItem;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.kradio.common.utils.ThreadUtil;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.constant.AdComponentConst;
import com.kaolafm.kradio.constant.AdControlProcessorConst;
import com.kaolafm.kradio.constant.AdExposeProcessConst;
import com.kaolafm.kradio.constant.HistoryComponentConst;
import com.kaolafm.kradio.constant.QueryHistoryProcessorConst;
import com.kaolafm.kradio.constant.SaveHistoryProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAclDisConnectedInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusListenerInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioBluetoothInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioPhoneStateInter;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.UrlUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.player.helper.intercept.AdPlayChainIntercept;
import com.kaolafm.kradio.player.helper.intercept.HintInterceptManager;
import com.kaolafm.kradio.player.manager.IBroadcastListChangeListener;
import com.kaolafm.opensdk.db.OnQueryListener;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.db.manager.RadioSortTypeDaoManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.ICheckCopyrightListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.model.BroadcastPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.TVPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.TimeDiscontinuousBroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.VideoPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.OneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.VideoAlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.RadioSortTypeItem;
import com.kaolafm.opensdk.player.logic.playlist.RadioPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.sdk.core.PlayListener;
import com.kaolafm.sdk.core.PlayState;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;
import static com.kaolafm.opensdk.api.live.model.LiveInfoDetail.STATUS_FINISHED;
import static com.kaolafm.opensdk.api.live.model.LiveInfoDetail.STATUS_LIVE_TO_RECORDING;
import static com.kaolafm.opensdk.api.live.model.LiveInfoDetail.STATUS_LIVING;
import static com.kaolafm.opensdk.player.logic.util.PlayerConstants.BROADCAST_STATUS_NOT_ON_AIR;
import static com.kaolafm.opensdk.player.logic.util.PlayerConstants.RESOURCES_TYPE_INVALID;
import static com.kaolafm.opensdk.player.logic.util.PlayerConstants.SORT_ACS;
import static com.kaolafm.opensdk.player.logic.util.PlayerConstants.SORT_DESC;

/**
 * 新播放器
 */
public class PlayerManagerHelper {

    private static final String TAG = "PlayerManagerHelper";

    // 1-断点续播；2-根据时间判断是否断点续播
    private static final String BREAK_POINT_CONTINUE_TIME = "2";

    /**
     * 快进快退时间间隔
     */
    private static final int FAST_SEEK_DURATION = 30 * 1000;

    private final BroadcastPlayerHelper mBroadcastPlayerHelper;

    //外调sdk注册的监听器，用于监听手动关闭
    private final List<PlayListener> mClientPlayListeners = new ArrayList<>();

    private final List<PlayListPayedListener> playListChangeListeners = new ArrayList<>();

    /**
     * 是否要显示loading
     */
    private boolean mShowLoading = false;

    private final PlayerStateCallBack playerStateCallBack;

    private volatile static boolean AudioAdPlayLockPlayer = false;

    private volatile static boolean AudioAdPlayOver = false;

    private volatile static boolean AudioUnPayLockPlayer = false;

    private volatile static boolean AudioPayedOver = false;

    private AudioAdvert audioAdvert;

    /**
     * 是否已经开始播放了
     */
    private boolean isFirstStartPlaying = false;

    private OnAudioFocusChangeInter mOnAudioFocusChangeInter;

    private KRadioPhoneStateInter mKRadioPhoneStateInter;

    private volatile boolean actionFromUser = false;

    private boolean refreshPlayListWithPlay = true; //默认不拦截播放

    private boolean mIsFromPlayPreInner = false;
    private boolean mIsFromPlayNextInner = false;

    /**
     * 是否在播放详情页中。对于广播，在播放详情页和首页的上下首按钮以及状态会有差别
     * 专辑/AI电台播放控制：
     * 与基线现有逻辑保持一致，仅支持切换所在播放专辑内单曲，不支持上一个下一个专辑或AI电台切换
     * 广播/电视直播流播放控制：
     * 非播放详情页面，上一个/下一个icon代表切换当前播放电台；
     * 播放详情页面内上一个/下一个icon，代表切换当前电台内的上一个与下一个节目，支持跨天切换上一个/下一个节目；
     * 直播间播放控制：
     * 上一个/下一个icon置灰展示
     **/
    private boolean isInProgramPlayerPage = false;

    public boolean isInProgramPlayerPage() {
        return isInProgramPlayerPage;
    }

    public void setInProgramPlayerPage(boolean inProgramPlayerPage) {
        isInProgramPlayerPage = inProgramPlayerPage;
    }

    public boolean isRefreshPlayListWithPlay() {
        return refreshPlayListWithPlay;
    }

    public void setRefreshPlayListWithPlay(boolean refreshPlayListWithPlay) {
        this.refreshPlayListWithPlay = refreshPlayListWithPlay;
    }

    public void resetRefreshPlayListWithPlay() {
        refreshPlayListWithPlay = true;
    }

    private final PlayerManager mPlayerManager;

    private PlayerManagerHelper() {
        mKRadioPhoneStateInter = ClazzImplUtil.getInter("KRadioPhoneStateImpl");
        mBroadcastPlayerHelper = new BroadcastPlayerHelper();
        playerStateCallBack = new PlayerStateCallBack();
        mPlayerManager = PlayerManager.getInstance();
        mPlayerManager.init(AppDelegate.getInstance().getContext());
        mPlayerManager.addPlayControlStateCallback(playerStateCallBack);
        isPlayerInitComplete = PlayerManager.getInstance().isPlayerInitSuccess();
        if (!isPlayerInitComplete) {
            mPlayerManager.addPlayerInitComplete(playerInitCompleteListener);
        }
        initBroadcastReceiver();
        initAudioFocusListener();
    }

    /**
     * 语音控制--播放订阅节目
     *
     * @param subscribeData
     */
    public void startSubscribe(SubscribeData subscribeData) {
        if (subscribeData == null) {
            return;
        }
        int type = subscribeData.getType();
        String id = String.valueOf(subscribeData.getId());
        PlayItem curPlayItem = getCurPlayItem();
        switch (type) {
            case ResType.ALBUM_TYPE:
                if (!curPlayItem.getAlbumId().equals(id)) {
                    start(id, PlayerConstants.RESOURCES_TYPE_ALBUM);
                }
                break;
            case ResType.FEATURE_TYPE:
                if (!curPlayItem.getAlbumId().equals(id)) {
                    start(id, PlayerConstants.RESOURCES_TYPE_FEATURE);
                }
                break;
            case ResType.AUDIO_TYPE:
                if (!String.valueOf(curPlayItem.getAudioId()).equals(id)) {
                    start(id, PlayerConstants.RESOURCES_TYPE_AUDIO);
                }
                break;
            case ResType.RADIO_TYPE:
                if (curPlayItem.getRadioId().equals(id)) {
                    start(id, PlayerConstants.RESOURCES_TYPE_RADIO);
                }
                break;
            case ResType.BROADCAST_TYPE:
                // no broadcast subscribe ,do nothing
            default:
                break;
        }
    }

    private static class PLAYER_HELPER_INNERCLASS {
        private static final PlayerManagerHelper INNER_CLASS = new PlayerManagerHelper();
    }

    public static PlayerManagerHelper getInstance() {
        return PLAYER_HELPER_INNERCLASS.INNER_CLASS;
    }

    public AudioAdvert getAudioAdvert() {
        return audioAdvert;
    }

    public void setAudioAdvert(AudioAdvert audioAdvert) {
        this.audioAdvert = audioAdvert;
    }

    /**
     * 锁定播放器
     */
    public void lockPlayerForAudioAd() {
        AudioAdPlayLockPlayer = true;
        AudioAdPlayOver = false;
    }

    /**
     * 锁定播放器，付费节目拦截调用
     */
    public void lockPlayerFromUnPay() {
        AudioUnPayLockPlayer = true;
        AudioPayedOver = false;
    }

    /**
     * 解除锁定播放器，支付成功回调调用
     */
    public void unlockPlayerFromUnPay() {
        AudioUnPayLockPlayer = false;
        AudioPayedOver = true;
    }

    /**
     * 根据购买成功返回的audIds来更新当前列表中PlayItem的支付状态
     */
    public void updatePayedItems(String audioIds) {
        List<Long> ids = convertAudioIdsToItemIdsList(audioIds);
        List<PlayItem> playItems = mPlayerManager.getPlayList();
        ThreadUtil.runOnThread(() -> {
            for (PlayItem item : playItems) {
                long audioId = item.getAudioId();
                for (Long id : ids) {
                    if (id == audioId) { //match the buy id
                        item.setBuyStatus(1); //change the buy id state
                    }
                }
            }
            ThreadUtil.runOnUI(() -> {
                notifyPlayListPayed(playItems);
            });
        });
    }

    /**
     * 专辑购买成功后调用，因为专辑购买是所以条目都解锁，为了避免重新请求网络，购买成功后把当前列表的所有item状态都改为已支付状态
     */
    public void updatePlayListItems2Payed() {
        List<PlayItem> playItems = mPlayerManager.getPlayList();
        ThreadUtil.runOnThread(() -> {
            for (PlayItem item : playItems) {
                item.setBuyStatus(1); //change the buy id state
            }
            ThreadUtil.runOnUI(() -> {
                notifyPlayListPayed(playItems);
            });
        });
    }

    /**
     * 通知支付状态变更
     *
     * @param playItems
     */
    private void notifyPlayListPayed(List<PlayItem> playItems) {
        for (PlayListPayedListener listener : playListChangeListeners) {
            listener.onPlayListPayed(playItems);
        }
    }

    public interface PlayListPayedListener {
        void onPlayListPayed(List<PlayItem> playItems);
    }

    public void addPlayListChangeListener(PlayListPayedListener listener) {
        if (listener != null && !playListChangeListeners.contains(listener)) {
            playListChangeListeners.add(listener);
        }
    }

    public void removePlayListChangeListener(PlayListPayedListener listener) {
        if (listener != null) {
            playListChangeListeners.remove(listener);
        }
    }

    /**
     * 将","分隔的id串拆分为list
     *
     * @param audioIds
     * @return
     */
    public List<Long> convertAudioIdsToItemIdsList(String audioIds) {
        boolean empty = StringUtil.isEmpty(audioIds);
        List<Long> ids = new ArrayList<>();
        if (empty) {
            return ids;
        } else {
            String[] idsStrs = audioIds.split(",");
            for (String idStr :
                    idsStrs) {
                long id = Long.parseLong(idStr);
                ids.add(id);
            }
            return ids;
        }
    }

    public boolean isAudioAdPlayLockOver() {
        if (AudioAdPlayLockPlayer) {
            if (NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.audioadplay_lock_play);
            } else {
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.no_net_work_str);
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * 播放广告完毕
     */
    public void playAudioAdOver(boolean isPlayAudioAdOver) {
        AudioAdPlayOver = isPlayAudioAdOver;
    }

    /**
     * 解除播放器锁定状态
     */
    public void removelockPlayerForAudioAd() {
        AudioAdPlayLockPlayer = false;
    }

    /**
     * 一键播放
     */
    public void playOneKeyListen() {
        playOneKeyListen(PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE);
    }

    public void playOneKeyListen(int type) {
        if (invalidPlayAction()) {
            return;
        }
        isFirstStartPlaying = true;
        finishAudioAd();
        playOneKeyListenInner(type);
    }

    /**
     * 跳过广告
     */
    public void jumpAd(boolean isNeedPlaying) {
        if (!AudioAdPlayOver) {
            if (audioAdvert != null) {
                PlayItem playItem = mPlayerManager.getCurrentTempTaskPlayItem();
                if (playItem instanceof AdPlayItem) {
                    ((AdPlayItem) playItem).setNeedNextInnerAction(true);
                    if (isNeedPlaying) {
                        ((AdPlayItem) playItem).setPlayerIsPlaying(true);
                    }
                }

                AdvertisingManager.getInstance().getReporter().skip(audioAdvert);
//                http://redmine.itings.cn/issues/40419
                AdvertisingManager.getInstance().close(audioAdvert);
            }
        }
    }

    public boolean isAudioAdPlayLockPlayer() {
        return AudioAdPlayLockPlayer;
    }

    private boolean isAudioUnPayLockPlayer() {
        return AudioUnPayLockPlayer;
    }

    /**
     * 音频广告播放，拦截播放器
     */
    public boolean lockPlayerIntercept() {
        PlayerLogUtil.log(getClass().getSimpleName(), "lockPlayerIntercept AudioAdPlayLockPlayer = " + AudioAdPlayLockPlayer);
        if (!isPlayingAd()) {
            PlayerLogUtil.log(getClass().getSimpleName(), "lockPlayerIntercept no ad play");
            return false;
        }

        if (AudioAdPlayLockPlayer) {
            if (NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.audioadplay_lock_play);
            } else {
                ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.no_net_work_str);
            }
        }
        return true;
    }

    public void finishAudioAd() {
//        新需求，场景是 图片广告过滤掉，不处理，只处理音图广告和音频广告
//        根据TempTaskPlayItem ，只有有音频广告，里面的 audio_type 就会在 AdvertPlayerImpl 赋值
//        包含定时广告，切换音频节目等等广告，通过判空区分是否当前的临时任务是否是音频广告
//        有音频广告
//        同时结束音频广告的生命周期，锁定期结束，播放状态完成

        if (isPlayingAd()) {
            PlayerLogUtil.log(getClass().getSimpleName(), "lockPlayerIntercept_finishAudioAd--> close_audio_ad ：" + AudioAdPlayOver);
            if (!AudioAdPlayOver) {
                //防止多次调用finishAudioAd 导致的多次上报
                AdvertisingManager.getInstance().getReporter().skip(audioAdvert);
            }
            playAudioAdOver(true);
            removelockPlayerForAudioAd();
            PlayerLogUtil.log(getClass().getSimpleName(), "finishAudioAd-->close");
            AdvertisingManager.getInstance().close(audioAdvert);
        }

    }

    /**
     * seek 进度
     *
     * @param position
     */
    public void seek(int position) {
        if (invalidPlayAction()) {
            return;
        }

        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            /**
             * 跳过广告, 再播放当前playitem. 再执行seek, ijk反应没有那么快, 导致seek命令执行失败.
             */
            PlayItem playItem = getCurPlayItem();
            playItem.setPosition(position);

            jumpAd(true);
            return;
        }
        mPlayerManager.seek(position);
    }

    /**
     * 播放历史
     *
     * @param historyItem
     */
    public void startHistory(HistoryItem historyItem) {
        YTLogUtil.logStart(TAG, "startHistory","");
        isFirstStartPlaying = true;
        startHistoryInner(historyItem);
    }

    public void startVideo(String id, int type, boolean isPlayNow) {
        ComponentClient.obtainBuilder(HistoryComponentConst.NAME)
                .setActionName(QueryHistoryProcessorConst.ACTION_QUERY)
                .addParam(QueryHistoryProcessorConst.KEY_ALBUM_ID, id)
                .isCallbackOnMainThread(true)
                .build()
                .callAsync((caller, result) -> {
                    Map<String, Object> data = result.getData();
                    if (data != null) {
                        //有播放历史，根据isBreakPointContinueTime判断是否断点续播
                        HistoryItem historyItem = (HistoryItem) data.get(QueryHistoryProcessorConst.KEY_RESULT_ALBUM_HISTORY);
                        if (historyItem != null) {
                            String audioId = historyItem.getAudioId();
                            long seekPosition = historyItem.getPlayedTime();
                            mPlayerManager.start(new VideoPlayerBuilder()
                                    .setSeekPosition(seekPosition)
                                    .setId(audioId)
                                    .setType(PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO)
                                    .setPlayNow(isPlayNow)
                            );
                        } else {
                            mPlayerManager.start(new VideoPlayerBuilder()
                                    .setId(id)
                                    .setType(type)
                                    .setPlayNow(isPlayNow)
                            );
                        }
                    } else {
                        mPlayerManager.start(new VideoPlayerBuilder()
                                .setId(id)
                                .setType(PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM)
                                .setPlayNow(isPlayNow)
                        );
                    }
                });
    }

    /**
     * 开始播放
     *
     * @param id
     * @param type
     */
    public void start(String id, int type) {
        start(id, type, 0, false, true);
    }

    /**
     * 开始播放
     *
     * @param id
     * @param type
     */
    public void start(String id, int type, boolean isPlayNow) {
        start(id, type, 0, false, isPlayNow);
    }

    public void start(String id, int type, long seekPosition, boolean isPlayNow) {
        start(id, type, seekPosition, false, isPlayNow);
    }

    /**
     * 检查版权
     *
     * @param id
     * @param type
     * @param iCheckCopyrightListener
     */
    public void checkCopyright(String id, int type, ICheckCopyrightListener iCheckCopyrightListener) {
        mPlayerManager.checkCopyright(new PlayerBuilder().setId(id).setType(type), iCheckCopyrightListener);
    }

    /**
     * 开始播放广播
     *
     * @param id
     * @param isIgnoreLocalRadio 当播放广播是是否忽略检查地方台，用于支持分区域的广播播放国家台时是否尝试获取当前经纬度所在省市的地方台。true：不尝试播放定位所在地的地方台
     */
    public void startBroadcast(String id, boolean isIgnoreLocalRadio) {
        start(id, PlayerConstants.RESOURCES_TYPE_BROADCAST, 0, isIgnoreLocalRadio, true);
    }

    /**
     * 开始播放
     *
     * @param id
     * @param type
     * @param isIgnoreLocalRadioWhenBroadcast 当播放广播是是否忽略检查地方台，用于支持分区域的广播播放国家台时是否尝试获取当前经纬度所在省市的地方台。true：不尝试播放定位所在地的地方台
     */
    private void start(String id, int type, long position, boolean isIgnoreLocalRadioWhenBroadcast, boolean isPlayNow) {
        if (invalidPlayAction()) {
            return;
        }
        isFirstStartPlaying = true;
        actionFromUser = true;
        finishAudioAd();
        startInner(id, type, position, isIgnoreLocalRadioWhenBroadcast, isPlayNow);
    }

    public void startTimeDiscontinuousBroadcastPlayItem(TimeDiscontinuousBroadcastPlayItem playItem) {
        if (playItem == null) return;
        if (invalidPlayAction()) {
            return;
        }
        isFirstStartPlaying = true;
        actionFromUser = true;
        finishAudioAd();
        mPlayerManager.startTimeDiscontinuousBroadcastPlayItem(playItem);
    }

    /**
     * 专门给语音唤醒后，调用外调sdk搜索接口，获取内容播放使用
     *
     * @param id
     * @param type
     */
    public void startSearchData(String id, int type) {
        if (invalidPlayAction()) {
            return;
        }
        isFirstStartPlaying = true;
        finishAudioAd();
        startInner(id, type);
    }

    /**
     * 快进30秒（语音调用）
     */
    public void fastForward() {
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(true);
        }
        fastForwardInner();
    }

    /**
     * 前进指定秒数（语音调用）
     *
     * @param seconds
     */
    public void fastForward(int seconds) {
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(true);
        }
        fastForwardInner(seconds);
    }


    /**
     * 后退30秒（语音调用）
     */
    public void fastBackward() {
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(true);
        }
        fastBackwardInner();
    }

    /**
     * 后退指定的秒数（语音调用）
     *
     * @param seconds
     */
    public void fastBackward(int seconds) {
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(true);
        }
        fastBackwardInner(seconds);
    }

    /**
     * 播放下一首
     *
     * @param fromUser
     */
    public void playNext(boolean fromUser) {
        //todo 播放到需要vip或付费内容时进行拦截判断
        this.actionFromUser = fromUser;
//        if (mPlayerManager.isAsyncStartExecuting()) {
//            PlayerLogUtil.log(getClass().getSimpleName(), "async start executing.");
//            return;
//        }
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(false);
        }
        if (!AntiShake.check("playNext")) {
            if (fromUser) {
                requestAudioFocus();
            }
            playNextInner();
        }
    }

    /**
     * 检修切换下个频道
     */
    public void playNextByOverhaul(boolean fromUser) {
        this.actionFromUser = fromUser;
//        if (mPlayerManager.isAsyncStartExecuting()) {
//            PlayerLogUtil.log(getClass().getSimpleName(), "async start executing.");
//            return;
//        }
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(false);
        }
        if (!AntiShake.check("playNext")) {
            if (fromUser) {
                requestAudioFocus();
            }
            playNextInnerByOverhaul();
        }
    }

    private boolean isProgramPage() {
        return isInProgramPlayerPage;
    }

    /**
     * 播放上一首
     *
     * @param fromUser
     */
    public void playPre(boolean fromUser) {
        this.actionFromUser = fromUser;
//        if (mPlayerManager.isAsyncStartExecuting()) {
//            return;
//        }
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(false);
        }
        if (!AntiShake.check("playPre")) {
            if (fromUser) {
                requestAudioFocus();
            }
            playPreInner();
        }

    }

    /**
     * 检修切换上个频道
     */
    public void playPreByOverhaul(boolean fromUser) {
        this.actionFromUser = fromUser;
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(false);
        }
        if (!AntiShake.check("playPre")) {
            if (fromUser) {
                requestAudioFocus();
            }
            playPreInnerByOverhaul();
        }
    }

    /**
     * 播放直播入流
     *
     * @param playItem
     */
    public void playStreamLiving(PlayItem playItem) {
//        if (mPlayerManager.isAsyncStartExecuting()) {
//            return;
//        }
        if (invalidPlayAction()) {
            return;
        }
        isFirstStartPlaying = true;
//        finishAudioAd();
        playStreamLivingInner(playItem);
    }

    /**
     * 暂停
     *
     * @param fromUser
     */
    public void pause(boolean fromUser) {
        this.actionFromUser = fromUser;
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(false);
            return;
        }

        mPlayerManager.pause(fromUser);
    }

    /**
     * 播放
     *
     * @param fromUser
     */
    public void play(boolean fromUser) {
        this.actionFromUser = fromUser;
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(true);
            return;
        }

        if (isInterceptPlay()) {
            playPlayListPosition();
        } else {
            mPlayerManager.play(fromUser);
        }

    }

    private void playPlayListPosition() {
        IPlayListControl playListControl = mPlayerManager.getPlayListControl();
        if (playListControl == null) {
            PlayerLogUtil.log(getClass().getSimpleName(), "playPlayListPosition", "playPlayListPosition playListControl is null");
            return;
        }
        PlayItem playItem = playListControl.getCurPlayItem();
        PlayerLogUtil.log(getClass().getSimpleName(), "playPlayListPosition", "playPlayListPosition playItem is " + playItem);
        if (playItem != null) {
            startPlayItemInList(playItem);
        }
    }

    /**
     * 是否拦截当前播放
     *
     * @return
     */
    private boolean isInterceptPlay() {
        PlayItem playItem = getCurPlayItem();
        return playItem == null || playItem.getType() == RESOURCES_TYPE_INVALID;
    }

    /**
     * 切换暂停与播放
     *
     * @param fromUser
     */
    public void switchPlayerStatus(boolean fromUser) {
        this.actionFromUser = fromUser;
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(true);
            return;
        }
        if (isAudioUnPayLockPlayer()) {
            PlayItem playItem = getCurPlayItem();
            mPlayerManager.startPlayItemInList(playItem, null);
            return;
        }
        if (isPlayingTempTask()) {
            PlayItem playItem = mPlayerManager.getCurrentTempTaskPlayItem();
            if (playItem instanceof TempTaskPlayItem) {
                ((TempTaskPlayItem) playItem).setPlayerIsPlaying(true);
                ((TempTaskPlayItem) playItem).setNeedNextInnerAction(true);
            }
            mPlayerManager.stopTempTask();
            return;
        }

        if (!isPlaying()) {
            PlayItem playItem = getCurPlayItem();
            if (playItem != null && playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
                if (playItem.getStatus() != STATUS_LIVING) {
                    return;
                }
            }
            play(fromUser);
            if (fromUser) {
                for (int i = 0; i < mClientPlayListeners.size(); i++) {
                    try {
                        PlayListener playListener = mClientPlayListeners.get(i);
                        if (playListener != null) {
                            PlayerLogUtil.log(getClass().getSimpleName(), "switchPlayerStatus", "onPlayStateChange: PLAYING, app inside");
                            playListener.onPlayStateChange(PlayState.PLAYING, 3, null);
                            Log.i(TAG, "switchPlayerStatus, playListener: " + playListener + ", hashCode: " + playListener.hashCode() + ", PlayState: " + PlayState.PLAYING + ", pid: " + Thread.currentThread().getId() + ", processID: " + Process.myPid());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } else {
            mPlayerManager.switchPlayerStatus(fromUser);
            //用户手动暂停，通知外调sdk
            if (fromUser) {
                PlayItem playItem = getCurPlayItem();
                for (int i = 0; i < mClientPlayListeners.size(); i++) {
                    try {
                        PlayListener playListener = mClientPlayListeners.get(i);
                        if (playListener != null) {
                            PlayerLogUtil.log(getClass().getSimpleName(), "switchPlayerStatus", "onPlayStateChange: PAUSED, app inside" + playItem);
                            playListener.onPlayStateChange(PlayState.PAUSED, 3, null);
                            Log.i(TAG, "switchPlayerStatus, playListener: " + playListener + ", hashCode: " + playListener.hashCode() + ", PlayState: " + PlayState.PAUSED + ", pid: " + Thread.currentThread().getId() + ", processID: " + Process.myPid());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 从列表里面点击播放
     */
    public void startPlayItemInList(PlayItem playItem) {
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(false);
        }
        mPlayerManager.startPlayItemInList(playItem, null);
    }

    public void startPlayItemInList(PlayItem playItem, VideoView videoView) {
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(false);
        }
        mPlayerManager.startPlayItemInList(playItem, videoView);
    }

    /**
     * 从列表里面点击播放
     */
    public void startPlayItemInList(PlayItem playItem, boolean isFromUser, VideoView videoView) {
        if (invalidPlayAction()) {
            return;
        }
        if (lockPlayerIntercept()) {
            if (isAudioAdPlayLockPlayer()) {
                return;
            }
            jumpAd(false);
        }
        this.actionFromUser = isFromUser;
        mPlayerManager.startPlayItemInList(playItem, videoView);
    }

    public void startPlayItemInList(PlayItem playItem, boolean isFromUser) {
        this.actionFromUser = isFromUser;
        startPlayItemInList(playItem);
    }

    /**
     * 当前是否正在播放 传入的碎片.
     *
     * @return
     */
    private boolean isPlayingCurrentPlayItem(HistoryItem item) {
        long historyAudioId = 0;
        try {
            historyAudioId = Long.parseLong(item.getAudioId());
        } catch (Exception e) {
        }

        long currentAudioId = getCurPlayItem().getAudioId();
        int currType = getCurPlayItem().getType();
        if (!String.valueOf(currType).equals(item.getType())) {
            //类型不同
            return false;
        }
        if (currType == PlayerConstants.RESOURCES_TYPE_TV || currType == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            //都是听电视或广播，注意：历史接口中听电视/广播的audioId是0
            return getCurPlayItem().getRadioId().equals(item.getRadioId());
        }
        if (currentAudioId == historyAudioId) {
            return isPlaying();
        }
        return false;
    }

    public boolean isBroadcastPlayer() {
        return getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_BROADCAST;
    }

    public boolean isTvPlayer() {
        return getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_TV;
    }

    public boolean isLivingPlayer() {
        return getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_LIVING;
    }

    public boolean isLivingStreamPlayer() {
        PlayItem playItem = getCurPlayItem();
        if (playItem == null) {
            return false;
        }
        return playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM;
    }


    //-------广播------

    public void addBroadcastRadioSimpleItem(BroadcastRadioSimpleData broadcastRadioSimpleData) {
        mBroadcastPlayerHelper.addBroadcastRadioSimpleItem(broadcastRadioSimpleData);
    }

    /**
     * 获取指定索引下广播对象
     *
     * @return
     */
    public BroadcastRadioSimpleData getBroadcastRadioSimpleDataByIndex(int index) {
        return mBroadcastPlayerHelper.getBroadcastRadioSimpleDataByIndex(index);
    }

    /**
     * 获取广播列表
     *
     * @return
     */
    public ArrayList<BroadcastRadioSimpleData> getBroadcastRadioSimpleDataArrayList() {
        return mBroadcastPlayerHelper.getBroadcastRadioSimpleDataArrayList();
    }


    public void addBroadcastListChangeListener(IBroadcastListChangeListener iBroadcastListChangeListener) {
        mBroadcastPlayerHelper.addBroadcastListChangeListener(iBroadcastListChangeListener);
    }

    public void removeBroadcastListChangeListener(IBroadcastListChangeListener iBroadcastListChangeListener) {
        mBroadcastPlayerHelper.removeBroadcastListChangeListener(iBroadcastListChangeListener);
    }

    //保存外调sdk注册的监听器
    public void addClientPlayListener(PlayListener iPlayListener) {
        mClientPlayListeners.add(iPlayListener);
    }

    public void removeClientPlayListener(PlayListener iPlayListener) {
        mClientPlayListeners.remove(iPlayListener);
    }

    /**
     * 新增一组广播列表数据
     *
     * @param list
     */
    public void addBroadcastRadioSimpleItems(ArrayList<BroadcastRadioSimpleData> list) {
        mBroadcastPlayerHelper.addBroadcastRadioSimpleItems(list); //todo
    }

    public ArrayList<BroadcastRadioSimpleData> getBroadcastRadioSimpleItems() {
        return mBroadcastPlayerHelper.getBroadcastRadioSimpleDataArrayList();
    }

    public void clearBroadcastRadioSimpleItems() {
        mBroadcastPlayerHelper.clearBroadcastRadioSimpleItems();
    }

    public int getCurrentFrequencyPosition() {
        return mBroadcastPlayerHelper.getCurrentFrequencyPosition();
    }

    public void setCurrentFrequencyPosition(int currentFrequencyPosition) {
        mBroadcastPlayerHelper.setCurrentFrequencyPosition(currentFrequencyPosition);
    }


    private static final String ERROR_PIC_1 = "default.jpg";
    private static final String ERROR_PIC_2 = "default.png";
    private static final String ERROR_PIC_3 = "default.jpeg";

    /**
     * 获取PlayItem pic url
     *
     * @param playItem
     * @return
     */
    public String getPlayItemPicUrl(PlayItem playItem) {
        String audioPic = playItem.getPicUrl();
        if (StringUtil.isEmpty(audioPic)) {
            return audioPic;
        }

        if (audioPic.endsWith(ERROR_PIC_1) || audioPic.endsWith(ERROR_PIC_2)
                || audioPic.endsWith(ERROR_PIC_3)) {
            audioPic = UrlUtil.getCustomPicUrl(UrlUtil.PIC_250_250, audioPic);
        }

        return audioPic;
    }

    /**
     * 是否是台宣
     *
     * @param playItem
     * @return
     */
    public boolean isRadioTaiXuan(PlayItem playItem) {
        if (playItem instanceof RadioPlayItem) {
            return ((RadioPlayItem) playItem).getRadioInfoData().getCategoryId() == PlayerConstants.CTG_TYPE_TX;
        }
        return false;
    }

    public long getSubscribeId() {
        return getSubscribeId(false);
    }

    /**
     * 统一获取订阅id的方法,因为一键收听的订阅id与众不同
     *
     * @return 如果不是有效的id, 则返回-1;
     */
    public long getSubscribeId(boolean isFromHomePlayerBar) {
        long subId = -1;

        PlayItem playItem = getCurPlayItem();
        boolean isAlbumItem = playItem instanceof AlbumPlayItem || playItem instanceof VideoAlbumPlayItem;
        Log.i(TAG, "getSubscribeId() --- isFromHomePlayerBar = " + isFromHomePlayerBar + ", isAlbumItem = " + isAlbumItem);

        if (playItem instanceof OneKeyPlayItem) {
            Log.i(TAG, "getSubscribeId() --- playItem is OneKeyPlayItem");
            subId = ((OneKeyPlayItem) playItem).getInfoData().getAlbumId();
        } else {
            Log.i(TAG, "getSubscribeId() --- playItem isn't OneKeyPlayItem");
            try {
                if (isFromHomePlayerBar && isAlbumItem) {
                    subId = Long.parseLong(playItem.getAlbumId());
                    boolean isAlbumIdValid = subId > 0;
                    Log.i(TAG, "getSubscribeId() --- isAlbumIdValid = " + isAlbumIdValid);
                    if (!isAlbumIdValid) {
                        subId = Long.parseLong(playItem.getRadioId());
                    }
                } else {
                    subId = Long.parseLong(playItem.getRadioId());
                    boolean isRadioIdValid = subId > 0;
                    Log.i(TAG, "getSubscribeId() --- isRadioIdValid = " + isRadioIdValid);
                }
            } catch (Exception e) {
                Log.e(TAG, "getSubscribeId() --- Exception = " + e);
                subId = -1;
            }
        }
        Log.i(TAG, "getSubscribeId() --- result --- isIdValid = " + (subId > 0));
        return subId;
    }

    public void resetVariable() {
        isFirstStartPlaying = false;
    }

    public void destroy() {
        AppDelegate.getInstance().getContext().unregisterReceiver(mHeadsetReceiver);
        mPlayerManager.removePlayControlStateCallback(playerStateCallBack);
        mPlayerManager.removePlayerInitComplete(playerInitCompleteListener);
        saveHistory();
        mPlayerManager.destroy();
        // 不再在播放器销毁时杀进程，避免切屏/生命周期过程中出现“自杀式”退出
        Log.i(TAG, "PlayerManagerHelper.destroy: player destroyed without killing process");
    }

    public void audioAdPlayOver(int durition) {
//      音频广告播放结束调用
        ComponentClient.obtainBuilder(AdComponentConst.NAME)
                .setActionName(AdControlProcessorConst.ACTION_CONTROL)
                .addParam(AdControlProcessorConst.AUDIO_AD_PLAY_OVER, AdControlProcessorConst.CANCEL)
                .addParam(AdControlProcessorConst.AUDIO_AD_PLAY_DURITION, durition)
                .isCallbackOnMainThread(true)
                .build()
                .call();
    }

    public void exposeAdforSwitchingPrograms(PlayItem playItem, AdPlayChainIntercept.IPlayAdEndCallback iPlayAdEndCallback) {
        ComponentClient.obtainBuilder(AdComponentConst.NAME)
                .setActionName(AdExposeProcessConst.ACTION_EXPOSE)
                .addParam(AdExposeProcessConst.KEY_EXPOSE_AD_PLAYITEM, playItem)
                .addParam(AdExposeProcessConst.KEY_EXPOSE_AD_CALLBACK, iPlayAdEndCallback)
                .isCallbackOnMainThread(true)
                .build()
                .call();
    }


    private void saveHistory() {
        ComponentClient.obtainBuilder(HistoryComponentConst.NAME)
                .setActionName(SaveHistoryProcessorConst.ACTION_UPLOAD)
                .addParam(SaveHistoryProcessorConst.KEY_PLAY_ITEM, getCurPlayItem())
                .addParam(SaveHistoryProcessorConst.KEY_DO_NOT_UPDATE_UI, false)
                .addParam(SaveHistoryProcessorConst.KEY_IS_SYNC_SAVE, true)
                .addParam(SaveHistoryProcessorConst.KEY_ITEM_TYPE, SaveHistoryProcessorConst.TYPE_QUIT_SAVE)
                .build().call();
        Logging.i(TAG, "saveHistory start");
    }

    private void playAlbumOrFeature(String id, int type, boolean isPlayNow) {
        if (type == PlayerConstants.RESOURCES_TYPE_ALBUM) {
            //专辑需要断点续播
            ComponentClient.obtainBuilder(HistoryComponentConst.NAME)
                    .setActionName(QueryHistoryProcessorConst.ACTION_QUERY)
                    .addParam(QueryHistoryProcessorConst.KEY_ALBUM_ID, id)
                    .isCallbackOnMainThread(true)
                    .build()
                    .callAsync((caller, result) -> {
                        Map<String, Object> data = result.getData();
                        if (data != null) {
                            //有播放历史，根据isBreakPointContinueTime判断是否断点续播
                            HistoryItem historyItem = (HistoryItem) data.get(QueryHistoryProcessorConst.KEY_RESULT_ALBUM_HISTORY);
                            if (historyItem != null) {
                                boolean isBreakPointContinueTime = BREAK_POINT_CONTINUE_TIME.equals(historyItem.getParamOne())
                                        && twoHoursLater(historyItem.getTimeStamp());
                                playAlbumOrFeatureImpl(isBreakPointContinueTime, type, historyItem.getRadioId(),
                                        historyItem.getAudioId(), historyItem.getPlayedTime(), isPlayNow);
                                return;
                            }
                        }
                        //没有播放历史，从头播
                        playAlbumOrFeatureImpl(true, type, id, null, 0, isPlayNow);
                    });
        } else if (type == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM) {
            //专辑需要断点续播
            ComponentClient.obtainBuilder(HistoryComponentConst.NAME)
                    .setActionName(QueryHistoryProcessorConst.ACTION_QUERY)
                    .addParam(QueryHistoryProcessorConst.KEY_ALBUM_ID, id)
                    .isCallbackOnMainThread(true)
                    .build()
                    .callAsync((caller, result) -> {
                        Map<String, Object> data = result.getData();
                        if (data != null) {
                            //有播放历史，根据isBreakPointContinueTime判断是否断点续播
                            HistoryItem historyItem = (HistoryItem) data.get(QueryHistoryProcessorConst.KEY_RESULT_ALBUM_HISTORY);
                            if (historyItem != null) {
                                playAlbumOrFeatureImpl(false, type, historyItem.getRadioId(),
                                        historyItem.getAudioId(), historyItem.getPlayedTime(), isPlayNow);
                                return;
                            }
                        }
                        //没有播放历史，从头播
                        playAlbumOrFeatureImpl(true, type, id, null, 0, isPlayNow);
                    });
        } else {
            //专题总是从头播放，没有断点续播
            playAlbumOrFeatureImpl(true, PlayerConstants.RESOURCES_TYPE_FEATURE, id, null, 0, isPlayNow);
        }
    }

    /**
     * 专辑/专题播放的真正实现代码
     *
     * @param isBreakPointContinueTime 根据时间确定断点续播，需要断点续播的专辑传false，其余情况传true。
     * @param type
     * @param radioId
     * @param audioId                  isBreakPointContinueTime为true时无效
     * @param playedTime               isBreakPointContinueTime为true时无效
     */
    private void playAlbumOrFeatureImpl(boolean isBreakPointContinueTime, int type, String radioId, String audioId, long playedTime, boolean isPlayNow) {
        YTLogUtil.logStart(TAG, "playAlbumOrFeatureImpl","start");
        if (isBreakPointContinueTime) {
            if (StringUtil.isEmpty(radioId)) {
                mPlayerManager.start(new PlayerBuilder()
                        .setId(radioId)
                        .setType(type)
                        .setPlayNow(isPlayNow));
            } else {
                getAlbumOrFeatureSortType(type, Long.parseLong(radioId), new Function1<Boolean, Unit>() {
                    @Override
                    public Unit invoke(Boolean aBoolean) {
                        mPlayerManager.start(new PlayerBuilder()
                                .setId(radioId)
                                .setType(type)
                                .setSort((aBoolean == null || aBoolean) ? SORT_ACS : SORT_DESC)
                                .setPlayNow(isPlayNow));
                        return null;
                    }
                });
            }
            return;
        }
        if (StringUtil.isEmpty(radioId)) {
            playWithSort(audioId, playedTime, radioId, type, null, isPlayNow);
        } else {
            getAlbumOrFeatureSortType(type, Long.parseLong(radioId), new Function1<Boolean, Unit>() {
                @Override
                public Unit invoke(Boolean aBoolean) {
                    playWithSort(audioId, playedTime, radioId, type, aBoolean, isPlayNow);
                    return null;
                }
            });
        }
        YTLogUtil.logStart(TAG, "playAlbumOrFeatureImpl","end");
    }

    private void playNextInnerByOverhaul() {
        int type = getCurrentPlayType();

        if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            ArrayList<BroadcastRadioSimpleData> tempPlayList = getBroadcastRadioSimpleDataArrayList();
            if (ListUtil.isEmpty(tempPlayList)) {
                return;
            }

            int position = getCurrentFrequencyPosition();
            if (position < tempPlayList.size()) {
                position++;
            }
            setCurrentFrequencyPosition(position);
            BroadcastRadioSimpleData broadcastRadioSimpleData = getBroadcastRadioSimpleDataByIndex(position);
            if (broadcastRadioSimpleData == null) {
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "playNextInner", "broadcast: id= " + broadcastRadioSimpleData.getBroadcastId());
            start(String.valueOf(broadcastRadioSimpleData.getBroadcastId()), PlayerConstants.RESOURCES_TYPE_BROADCAST);
        } else {
            mPlayerManager.playNext();
        }
    }

    private boolean isFromPlayPreOrNextInnerFlag() {
        return mIsFromPlayPreInner || mIsFromPlayNextInner;
    }

    private void resetFromPlayPreOrNextInnerFlag() {
        mIsFromPlayPreInner = false;
        mIsFromPlayNextInner = false;
    }

    private void playNextInner() {
        int type = getCurrentPlayType();

        if (!isProgramPage() && (type == PlayerConstants.RESOURCES_TYPE_BROADCAST || type == PlayerConstants.RESOURCES_TYPE_TV)) {
            ArrayList<BroadcastRadioSimpleData> tempPlayList = getBroadcastRadioSimpleDataArrayList();
            if (ListUtil.isEmpty(tempPlayList)) {
                return;
            }

            int position = getCurrentFrequencyPosition();
            if (position < tempPlayList.size()) {
                position++;
            }
            setCurrentFrequencyPosition(position);
            BroadcastRadioSimpleData broadcastRadioSimpleData = getBroadcastRadioSimpleDataByIndex(position);
            if (broadcastRadioSimpleData == null) {
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "playNextInner", "broadcast: id= " + broadcastRadioSimpleData.getBroadcastId());
            mIsFromPlayNextInner = true;
            start(String.valueOf(broadcastRadioSimpleData.getBroadcastId()), broadcastRadioSimpleData.getResType());
        } else {
            mPlayerManager.playNext();
        }
    }

    private void playPreInnerByOverhaul() {
        int type = getCurrentPlayType();

        if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            ArrayList<BroadcastRadioSimpleData> tempPlayList = getBroadcastRadioSimpleDataArrayList();
            if (ListUtil.isEmpty(tempPlayList)) {
                return;
            }

            int position = getCurrentFrequencyPosition();
            if (position == 0) {
                Log.d(TAG, "playPreInnerByOverhaul(), already first");
                return;
            }
            if (position > 0) {
                position--;
            }
            setCurrentFrequencyPosition(position);
            BroadcastRadioSimpleData broadcastRadioSimpleData = getBroadcastRadioSimpleDataByIndex(position);
            if (broadcastRadioSimpleData == null) {
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "playPreInner", "broadcast: id= " + broadcastRadioSimpleData.getBroadcastId());
            start(String.valueOf(broadcastRadioSimpleData.getBroadcastId()), PlayerConstants.RESOURCES_TYPE_BROADCAST);
        } else {
            mPlayerManager.playPre();
        }
    }

    private void playPreInner() {
        int type = getCurrentPlayType();

        if (!isProgramPage() && (type == PlayerConstants.RESOURCES_TYPE_BROADCAST || type == PlayerConstants.RESOURCES_TYPE_TV)) {
            ArrayList<BroadcastRadioSimpleData> tempPlayList = getBroadcastRadioSimpleDataArrayList();
            if (ListUtil.isEmpty(tempPlayList)) {
                return;
            }

            int position = getCurrentFrequencyPosition();
            if (position > 0) {
                position--;
            }
            setCurrentFrequencyPosition(position);
            BroadcastRadioSimpleData broadcastRadioSimpleData = getBroadcastRadioSimpleDataByIndex(position);
            if (broadcastRadioSimpleData == null) {
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "playPreInner", "broadcast: id= " + broadcastRadioSimpleData.getBroadcastId());
            mIsFromPlayPreInner = true;
            start(String.valueOf(broadcastRadioSimpleData.getBroadcastId()), broadcastRadioSimpleData.getResType());
        } else {
            mPlayerManager.playPre();
        }
    }

    /**
     * 是否有下一个
     *
     * @return
     */
    public boolean hasNextItem() {
        int type = getCurrentPlayType();
        if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST
                || type == PlayerConstants.RESOURCES_TYPE_TV) {
            return hasNext();
        }
        return hasNext() || hasNextPage();
    }

    /**
     * 是否有上一个
     *
     * @return
     */
    public boolean hasPreItem() {
        int type = getCurrentPlayType();
        if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST
                || type == PlayerConstants.RESOURCES_TYPE_TV) {
            return hasPre();
        }
        return hasPre() || hasPrePage();
    }

    private boolean hasNext() {
        if (isProgramPage()) {
            return hasNextProgramItem();
        }
        int type = getCurrentPlayType();
        if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST
                || type == PlayerConstants.RESOURCES_TYPE_TV) {
            int index = PlayerManagerHelper.getInstance().getCurrentFrequencyPosition();
            int size = PlayerManagerHelper.getInstance().getBroadcastRadioSimpleDataArrayList().size();
            if (size <= 1) {
                return false;
            }
            return index + 1 < size;
        }

        return mPlayerManager.hasNext();
    }

    public boolean hasPreProgramItem() {
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            PlaylistInfo playListInfo = mPlayerManager.getPlayListInfo();
            //播单不可见
            if (playListInfo == null || playListInfo.getProgramEnable() == PlayerConstants.BROADCAST_PROGRAM_HIDE)
                return false;
        }
        return mPlayerManager.hasPre();
    }

    public boolean hasNextProgramItem() {
        //对于广播，入股已经切换到了正在直播的广播节目，则应无法继续下一首
        int customType = mPlayerManager.getCustomType();
        if (customType == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            PlaylistInfo playListInfo = mPlayerManager.getPlayListInfo();
            //播单不可见
            if (playListInfo == null || playListInfo.getProgramEnable() == PlayerConstants.BROADCAST_PROGRAM_HIDE)
                return false;
        }

        if (customType == PlayerConstants.RESOURCES_TYPE_BROADCAST
                || customType == PlayerConstants.RESOURCES_TYPE_TV) {
            PlayItem curPlayItem = getCurPlayItem();
            if (curPlayItem.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
                return false;
            }
        }
        return mPlayerManager.hasNext();
    }

    /**
     * 广播节目：是否未开始
     *
     * @param playItem
     * @return
     */
    public boolean isNotStartProgram(@NonNull PlayItem playItem) {
        if (playItem == null) return false;
        return playItem.getStatus() == BROADCAST_STATUS_NOT_ON_AIR;
    }

    /**
     * 直播节目：是否已结束
     *
     * @param playItem
     * @return
     */
    public boolean isFinishedLive(@NonNull PlayItem playItem) {
        if (playItem == null) return false;
        return playItem.getStatus() == STATUS_FINISHED;
    }

    /**
     * 直播节目：是否未开始
     *
     * @param playItem
     * @return
     */
    public boolean isNotStartLive(@NonNull PlayItem playItem) {
        if (playItem == null) return false;
        return playItem.getStatus() != STATUS_FINISHED && playItem.getStatus() != STATUS_LIVING && playItem.getStatus() != STATUS_LIVE_TO_RECORDING;
    }

    private boolean hasPre() {
        if (isProgramPage()) {
            return hasPreProgramItem();
        }
        int type = getCurrentPlayType();
        if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST
                || type == PlayerConstants.RESOURCES_TYPE_TV) {
            return PlayerManagerHelper.getInstance().getCurrentFrequencyPosition() > 0;
        }

        return mPlayerManager.hasPre();
    }

    public PlayItem getNextPlayItem() {
        if (mPlayerManager.hasNext()) {
            int nextIndex = (mPlayerManager.getPlayListCurrentPosition() + 1);
            if (nextIndex >= mPlayerManager.getPlayList().size()) {
                return new InvalidPlayItem();
            }
            return mPlayerManager.getPlayList().get(nextIndex);
        }
        return new InvalidPlayItem();
    }

    public PlayItem getPrevPlayItem() {
        if (mPlayerManager.hasPre()) {
            int prevIndex = (mPlayerManager.getPlayListCurrentPosition() - 1);
            if (prevIndex < 0) {
                return new InvalidPlayItem();
            }
            return mPlayerManager.getPlayList().get(prevIndex);
        }
        return new InvalidPlayItem();
    }

    private boolean hasNextPage() {
        PlayItem playItem = getCurPlayItem();
        if (playItem != null && playItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        return mPlayerManager.hasNextPage();
    }

    private boolean hasPrePage() {
        PlayItem playItem = getCurPlayItem();
        if (playItem != null && playItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        return mPlayerManager.hasPrePage();
    }

    /**
     * 【拖动进度条】播放器处于播放加载中状态，返回首页再回播放器，播放器处于播放状态了。
     * #39243
     */
    private boolean isBuffering = false;

    public boolean isPlayerBuffering() {
        return isBuffering;
    }

    // 监控播放器是否初始化完成
    private boolean isPlayerInitComplete = false;
    private final ArrayList<IPlayerInitCompleteListener> mPlayerInitCompleteListeners = new ArrayList<>();
    private final IPlayerInitCompleteListener playerInitCompleteListener = b -> {
        YTLogUtil.logStart(TAG, "onPlayerInitComplete", "b=" + b);
        isPlayerInitComplete = b;
        mPlayerInitCompleteListeners.forEach(listener -> listener.onPlayerInitComplete(b));
    };

    public boolean isPlayerInitComplete() {
        return isPlayerInitComplete;
    }

    public void addPlayerInitCompleteListener(IPlayerInitCompleteListener listener) {
        if (listener != null) {
            mPlayerInitCompleteListeners.add(listener);
        }
    }

    public void removePlayerInitCompleteListener(IPlayerInitCompleteListener listener) {
        if (listener != null) {
            mPlayerInitCompleteListeners.remove(listener);
        }
    }

    // 监控播放器状态

    private class PlayerStateCallBack extends BasePlayStateListener {

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            YTLogUtil.logStart(TAG, "onPlayerPreparing", "");
            mShowLoading = true;
        }

        @Override
        public void onIdle(PlayItem playItem) {
            mShowLoading = true;
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            mShowLoading = false;
        }

        @Override
        public void onSeekStart(PlayItem playItem) {
            mShowLoading = false;
        }

        @Override
        public void onSeekComplete(PlayItem playItem) {
            mShowLoading = false;
        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {
            isBuffering = false;
            mShowLoading = false;
        }

        @Override
        public void onBufferingStart(PlayItem playItem) {
            isBuffering = true;
            mShowLoading = true;
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            isBuffering = false;
            mShowLoading = false;
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i1, int i) {
            isBuffering = false;
            mShowLoading = false;
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            YTLogUtil.logStart(TAG, "onPlayerPlaying", "");
            YTLogUtil.logStartEnd();
            isBuffering = false;
            mShowLoading = false;
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long l) {
            isBuffering = false;
            mShowLoading = false;
        }
    }


    private void initBroadcastReceiver() {
        IntentFilter intentFilter = new IntentFilter(
                Intent.ACTION_HEADSET_PLUG);
        // 监视蓝牙关闭和打开的状态
        intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
        // 监视蓝牙设备与APP连接的状态
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);

        AppDelegate.getInstance().getContext().registerReceiver(mHeadsetReceiver, intentFilter);
    }

    private boolean headsetConnected = false;

    private final BroadcastReceiver mHeadsetReceiver = new BroadcastReceiver() {
        private static final String STATE = "state";

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.i("mHeadsetReceiver", "action: " + action);
            if (Intent.ACTION_HEADSET_PLUG.equals(action)) {
                if (intent.hasExtra(STATE)) {
                    if (headsetConnected && intent.getIntExtra(STATE, 0) == 0) {
                        headsetConnected = false;
                        KRadioBluetoothInter kRadioBluetoothInter = ClazzImplUtil.getInter("KRadioBluetoothImpl");
                        if (kRadioBluetoothInter == null || kRadioBluetoothInter.needAutoPause()) {
                            mPlayerManager.pause();
                        }
                    } else if (!headsetConnected && intent.getIntExtra(STATE, 0) == 1) {
                        headsetConnected = true;
                    }
                }
            } else if (BluetoothDevice.ACTION_ACL_DISCONNECTED.equals(action)) {
                KRadioAclDisConnectedInter kRadioAclDisConnectedInter = ClazzImplUtil.getInter("KRadioAclDisConnectedImpl");
                //解决https://app.huoban.com/tables/2100000007530121/items/2300002119631835问题
                if (kRadioAclDisConnectedInter == null || !kRadioAclDisConnectedInter.doAclDisConnected(intent)) {
                    mPlayerManager.pause();
                }
            }
        }
    };

    private void fastBackwardInner() {
        PlayItem curPlayItem = getCurPlayItem();
        if (curPlayItem == null) {
            return;
        }

        int position = curPlayItem.getPosition();
        if (position > FAST_SEEK_DURATION) {
            position -= FAST_SEEK_DURATION;
            mPlayerManager.seek(position);
        } else {
            mPlayerManager.seek(0);
        }
    }

    private void fastBackwardInner(int seconds) {
        int timemills = seconds * 1000;
        PlayItem curPlayItem = getCurPlayItem();
        if (curPlayItem == null) {
            return;
        }

        int position = curPlayItem.getPosition();
        if (position > timemills) {
            position -= timemills;
            mPlayerManager.seek(position);
        } else {
            mPlayerManager.seek(0);
        }
    }

    private void fastForwardInner() {
        PlayItem curPlayItem = getCurPlayItem();
        if (curPlayItem == null) {
            return;
        }
        if (curPlayItem.isLiving()) {
            return;
        }

        int position = curPlayItem.getPosition();
        if (position > 0) {
            position += FAST_SEEK_DURATION;
            int duration = curPlayItem.getDuration();
            if (position > duration) {
                position = duration;
            }
            mPlayerManager.seek(position);
        }
    }


    private void fastForwardInner(int seconds) {
        int timemills = seconds * 1000;
        PlayItem curPlayItem = getCurPlayItem();
        if (curPlayItem == null) {
            return;
        }
        if (curPlayItem.isLiving()) {
            return;
        }

        int position = curPlayItem.getPosition();
        Log.i("fastForwardInner", "currentPosition:" + position);
        Log.i("fastForwardInner", "forwardSeconds:" + seconds);
        if (position > 0) {
            position += timemills;
            int duration = curPlayItem.getDuration();
            if (position > duration) {
                position = duration;
            }
            Log.i("fastForwardInner", "seekToPosition:" + position);
            mPlayerManager.seek(position);
        }
    }

    /**
     * 一键播放
     */
    private void playOneKeyListenInner() {
        playOneKeyListenInner(PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE);
    }

    private void playOneKeyListenInner(int type) {
        clearBroadcastRadioSimpleItems();
        start(String.valueOf(ResType.NOACTION_TYPE), type);
    }

    /**
     * 播放历史
     *
     * @param historyItem
     */
    private void startHistoryInner(HistoryItem historyItem) {
        YTLogUtil.logStart(TAG, "startHistoryInner","start");
        if (historyItem == null) {
            return;
        }

        if (isPlayingCurrentPlayItem(historyItem)) {
            return;
        }
        int type = Integer.parseInt(historyItem.getType());
        YTLogUtil.logStart(TAG, "startHistoryInner",
                "id = " + historyItem.getAudioId()
                + " time = " + historyItem.getPlayedTime()
                + " album id= " + historyItem.getRadioId()
                + " type = " + historyItem.getType());
        if (type != PlayerConstants.RESOURCES_TYPE_BROADCAST && type != PlayerConstants.RESOURCES_TYPE_TV) {
            clearBroadcastRadioSimpleItems();
        }

        if (type == PlayerConstants.RESOURCES_TYPE_ALBUM) {
            boolean isBreakPointContinueTime = BREAK_POINT_CONTINUE_TIME.equals(historyItem.getParamOne())
                    && twoHoursLater(historyItem.getTimeStamp());
            playAlbumOrFeatureImpl(isBreakPointContinueTime, type, historyItem.getRadioId(), historyItem.getAudioId(), historyItem.getPlayedTime(), true);
        } else if (type == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM) {
            playAlbumOrFeatureImpl(false, type, historyItem.getRadioId(), historyItem.getAudioId(), historyItem.getPlayedTime(), true);
        } else if (type == PlayerConstants.RESOURCES_TYPE_FEATURE) {
            //专题都不断点续播
            playAlbumOrFeatureImpl(true, type, historyItem.getRadioId(), historyItem.getAudioId(), historyItem.getPlayedTime(), true);
        } else {
            YTLogUtil.logStart(TAG, "startHistoryInner","Player start!!! id = " + historyItem.getRadioId());
            mPlayerManager.start(new PlayerBuilder().setId(historyItem.getRadioId()).setType(type).setPlayNow(true));
        }
        YTLogUtil.logStart(TAG, "startHistoryInner","end");
    }

    /**
     * 开始播放专辑，支持设置排序方式
     *
     * @param audioId
     * @param seekPosition
     * @param id
     * @param type
     * @param sortIdAcs    null:使用sdk中PlayerBuilder定义的默认排序方式，{@link PlayerConstants#SORT_ACS}升序，{@link PlayerConstants#SORT_DESC}降序
     */
    private void playWithSort(String audioId, long seekPosition, String id, int type, Boolean sortIdAcs, boolean isPlayNow) {
        Log.d(TAG, "按照排序播放专辑,sortIdAcs:" + sortIdAcs);
        PlayerBuilder playerBuilder;
        if (sortIdAcs == null) {
            playerBuilder = new CustomPlayerBuilder()
                    .setChildId(audioId)
                    .setSeekPosition(seekPosition)
                    .setId(id)
                    .setType(type)
                    .setPlayNow(isPlayNow);
        } else {
            int sortType = sortIdAcs ? SORT_ACS : SORT_DESC;
            playerBuilder = new CustomPlayerBuilder()
                    .setChildId(audioId)
                    .setSeekPosition(seekPosition)
                    .setId(id)
                    .setType(type)
                    .setSort(sortType)
                    .setPlayNow(isPlayNow);
        }
        mPlayerManager.start(playerBuilder);
    }

    /**
     * 查询音频专辑/视频专辑/专题的播单排序方式
     *
     * @param type
     * @param id
     * @param sortIsAcs
     */
    public void getAlbumOrFeatureSortType(int type, long id, Function1<Boolean, Unit> sortIsAcs) {
        RadioSortTypeDaoManager.getInstance().query(type, id, new OnQueryListener<RadioSortTypeItem>() {
            @Override
            public void onQuery(RadioSortTypeItem radioSortTypeItem) {
                Log.d(TAG, "查询排序方式：" + new Gson().toJson(radioSortTypeItem));
                Boolean sortTypeIsAcs;
                if (radioSortTypeItem == null || (radioSortTypeItem.getSortType() != PlayerConstants.SORT_ACS && radioSortTypeItem.getSortType() != SORT_DESC)) {
                    //数据库里没有保存
                    sortTypeIsAcs = null;
                } else {
                    if (radioSortTypeItem.getSortType() == PlayerConstants.SORT_ACS) {
                        //正序
                        sortTypeIsAcs = true;
                    } else {
                        sortTypeIsAcs = false;
                    }
                }
                if (sortIsAcs != null) sortIsAcs.invoke(sortTypeIsAcs);
            }
        });
    }

    private boolean twoHoursLater(long time) {
        long currentTime = System.currentTimeMillis();
        long result = currentTime - time;
        Logger.i(TAG, "current=" + currentTime + ":" + time);
        return result > 2 * 60 * 60 * 1000;
    }

    public void calculateCurrentFrequencyPosition(long id) {
        mBroadcastPlayerHelper.calculateCurrentFrequencyPosition(id);
    }

    /**
     * 开始播放
     *
     * @param id
     * @param type
     */
    private void startInner(String id, int type) {
        startInner(id, type, 0, false, false);
    }

    /**
     * 开始播放
     *
     * @param id
     * @param type
     */
    private void startInner(String id, int type, long position, boolean isIgnoreLocalRadioWhenBroadcast, boolean isPlayNow) {
        PlayerLogUtil.log(getClass().getSimpleName(), "startInner", "id = " + id + " type= " + type);
        if (type == PlayerConstants.RESOURCES_TYPE_ALBUM ||
                type == PlayerConstants.RESOURCES_TYPE_FEATURE) {
            clearBroadcastRadioSimpleItems();
            playAlbumOrFeature(id, type, isPlayNow);
            return;
        }

        if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST
                || (isFromPlayPreOrNextInnerFlag() && type == PlayerConstants.RESOURCES_TYPE_TV)) {
            try {
                long tempLong = Long.parseLong(id);
                mBroadcastPlayerHelper.calculateCurrentFrequencyPosition(tempLong);
            } catch (Exception e) {

            }
        } else {
            clearBroadcastRadioSimpleItems();
        }
        resetFromPlayPreOrNextInnerFlag();
        if (type == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            mPlayerManager.start(
                    new BroadcastPlayerBuilder()
                    .setIgnoreLocalRadio(isIgnoreLocalRadioWhenBroadcast)
                            .setSeekPosition(position)
                            .setId(id)
                            .setType(type));
        } else if (type == PlayerConstants.RESOURCES_TYPE_TV) {
            mPlayerManager.start(new TVPlayerBuilder().setId(id).setType(type));
        } else {
            mPlayerManager.start(new PlayerBuilder().setId(id).setType(type));
        }
    }

    private void playStreamLivingInner(PlayItem playItem) {
        clearBroadcastRadioSimpleItems();
        IPlayListControl listControl = mPlayerManager.getPlayListControl();
        if (listControl instanceof RadioPlayListControl) {
            ((RadioPlayListControl) listControl).addSongPlayItem(playItem);
            start(String.valueOf(playItem.getAudioId()), PlayerConstants.RESOURCES_TYPE_RADIO);
        }
    }

    public int getCurrentPlayType() {
        int type = getCurPlayItem().getType();
        if (type == RESOURCES_TYPE_INVALID) {
            type = mPlayerManager.getCustomType();
        }
        return type;
    }

    /**
     * 当前播放器是否存在可播放资源
     *
     * @return true为是，false为否
     */
    public boolean isPlayerResAavalible() {
        int type = getCurPlayItem().getType();
        return type != RESOURCES_TYPE_INVALID;
    }

    public boolean isFirstStartPlaying() {
        return isFirstStartPlaying;
    }


    public boolean isPlaying() {
        if (isPlayingAd()) {
            return false;
        }
        return mPlayerManager.isPlaying();
    }

    /**
     * 当前播放的是否是广告
     *
     * @return
     */
    public boolean isPlayingAd() {
        PlayItem tempTaskPlayItem = mPlayerManager.getCurrentTempTaskPlayItem();
        if (tempTaskPlayItem == null) {
            return false;
        }
        if (tempTaskPlayItem.getType() != PlayerConstants.RESOURCES_TYPE_TEMP_TASK) {
            return false;
        }
        return (tempTaskPlayItem instanceof AdPlayItem);
    }

    public void stopAudioAd(boolean b) {
        if (isPlayingAd()) {
            mPlayerManager.stopTempTask(b);
        }
    }

    private void initAudioFocusListener() {
        PlayerLogUtil.log(getClass().getSimpleName(), "initAudioFocusListener");
        mOnAudioFocusChangeInter = focusChange -> {
            KRadioAudioFocusListenerInter inter = ClazzImplUtil.getInter("KRadioAudioFocusListenerImpl");
            if (inter != null) {
                inter.onFocusChanged(focusChange);
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "audio focus change :" + focusChange);
            if (focusChange == AUDIOFOCUS_LOSS || focusChange == AUDIOFOCUS_LOSS_TRANSIENT) {
                if (isPlayingAd()) {
                    PlayItem playItem = mPlayerManager.getCurrentTempTaskPlayItem();
                    if (playItem instanceof AdPlayItem) {
                        ((AdPlayItem) playItem).setNeedNextInnerAction(true);
                        ((AdPlayItem) playItem).setPlayerIsPlaying(false);
                    }
                    PlayerLogUtil.log(getClass().getSimpleName(), "audio focus change, close ad");
                    finishAudioAd();
                }
            }
        };

        mPlayerManager.addAudioFocusListener(mOnAudioFocusChangeInter);
    }

    /**
     * 判断是否当前正在播放的专辑,PGC,广播,音乐
     *
     * @param id
     * @return
     */
    public boolean isPlayCurrentRadio(String id) {
        if (StringUtil.isEmpty(id)) {
            return false;
        }
        PlayItem playItem = getCurPlayItem();
        if (playItem == null) {
            return false;
        }
        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE) {
            return false;
        }
        String radioId = playItem.getRadioId();
        if (StringUtil.isEmpty(radioId)) {
            return false;
        }
        return radioId.equals(id);
    }

    /**
     * 当前是否播放的临时任务, 非广告
     *
     * @return
     */
    public boolean isPlayingTempTask() {
        PlayItem playItem = mPlayerManager.getCurrentTempTaskPlayItem();
        if (playItem instanceof AdPlayItem) {
            return false;
        }
        return playItem instanceof TempTaskPlayItem;
    }


    /**
     * 失效的播放动作, 如果检测到当前正在打电话 , 就不能点击
     */
    public boolean invalidPlayAction() {
        boolean calling = false;
        if (mKRadioPhoneStateInter == null) {
            TelephonyManager telephonyManager = (TelephonyManager) AppDelegate.getInstance().getContext().getSystemService(Context.TELEPHONY_SERVICE);
            PlayerLogUtil.log(getClass().getSimpleName(), "invalidPlayAction", "state =" + telephonyManager.getCallState());
            if (TelephonyManager.CALL_STATE_OFFHOOK == telephonyManager.getCallState() || TelephonyManager.CALL_STATE_RINGING == telephonyManager.getCallState()) {
                PlayerLogUtil.log(getClass().getSimpleName(), "invalidPlayAction", "on call");
                calling = true;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "invalidPlayAction", "is calling : " + calling);
        } else {
            calling = mKRadioPhoneStateInter.isInCall();
            PlayerLogUtil.log(getClass().getSimpleName(), "invalidPlayAction", "calling =" + calling);
        }
        return calling;
    }

    public boolean canShowLoading() {
        return mShowLoading && !isPlayingAd();
    }

    public boolean isPlayingClock() {
        PlayItem playItem = mPlayerManager.getCurrentTempTaskPlayItem();
        if (playItem != null && playItem.getType() == PlayerConstants.RESOURCES_TYPE_TEMP_TASK) {
            TempTaskPlayItem tempTaskPlayItem = (TempTaskPlayItem) playItem;
            return tempTaskPlayItem.getTempTaskType() == PlayerConstants.TEMP_TASK_TYPE_CLOCK
                    || tempTaskPlayItem.getTempTaskType() == PlayerConstants.TEMP_TASK_TYPE_HINT;
        }
        return false;
    }

    /**
     * 获取广播播单
     *
     * @param notBroadcastProgram
     * @return
     */
    public List<PlayItem> getPlayList(boolean notBroadcastProgram) {
        if (notBroadcastProgram) {
            ArrayList<PlayItem> arrayList = new ArrayList<>();
            ArrayList<BroadcastRadioSimpleData> broadcastRadioSimpleData = getBroadcastRadioSimpleDataArrayList();
            if (broadcastRadioSimpleData == null) {
                return null;
            }
            int size = broadcastRadioSimpleData.size();
            for (int i = 0; i < size; i++) {
                BroadcastRadioSimpleData tempData = broadcastRadioSimpleData.get(i);
                arrayList.add(tempData.transToPlayItem());
            }
            return arrayList;
        } else {
            return mPlayerManager.getPlayList();
        }
    }

    /**
     * 检查是否有广播播单
     *
     * @return
     */
    public boolean isHasBroadcastPlayList() {
        boolean isHasPlayList = true;
        List<PlayItem> playItemList = mPlayerManager.getPlayList();
        if (ListUtil.isEmpty(playItemList)) {
            return false;
        }
        if (PlayerManagerHelper.getInstance().isBroadcastPlayer() || PlayerManagerHelper.getInstance().isTvPlayer()) {
            PlayItem playItem = playItemList.get(0);
            if (playItem.getAudioId() == 0 && !(playItem instanceof TimeDiscontinuousBroadcastPlayItem)) {
                return false;
            }
        }
        if (PlayerManagerHelper.getInstance().isBroadcastPlayer()) {
            PlaylistInfo playListInfo = mPlayerManager.getPlayListInfo();
            //播单不可见
            if (playListInfo == null || playListInfo.getProgramEnable() == PlayerConstants.BROADCAST_PROGRAM_HIDE) {
                return false;
            }
        }
        return isHasPlayList;
    }

    public void requestAudioFocus() {
        int currentFocus = mPlayerManager.getCurrentAudioFocusStatus();
        if (currentFocus < 0) {
            mPlayerManager.requestAudioFocus();
        }
    }

    /**
     * 获取当前播放事件是否是用户触发的
     *
     * @return
     */
    public boolean isActionFromUser() {
        return actionFromUser;
    }

    /**
     * 重置播放事件的播放是否由用户触发的状态
     */
    public void resetActionFromUser() {
        actionFromUser = false;
    }


    private void resetPlayListControl() {
        mPlayerManager.resetPlayListControl();
    }

    /**
     * 重置播放列表，重新拉取播放播放
     *
     * @param id
     * @param type
     */
    public void restart(String id, int type) {
        resetPlayListControl();
        start(id, type);
    }

    /**
     * 刷新播放列表，重新播放
     */
    public void refreshPlayList() {
        //获取到拦截播放的条目
        PlayItem playItem = HintInterceptManager.getInstance().getInterceptPlayItem();
        if (playItem == null) {
            playItem = getCurPlayItem();
        }
        if (playItem != null) {
            String albumId = playItem.getAlbumId();
            restart(albumId, playItem.getType());
        }
    }

    /**
     * 获取playId，用于跳转播放页时进行对比
     *
     * @param playItem
     * @return
     */
    public long getPlayId(PlayItem playItem) {
        long id = 0;
        if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
            LivePlayItem livePlayItem = (LivePlayItem) playItem;
            id = livePlayItem.getLiveId();
        } else if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST
                || playItem.getType() == PlayerConstants.RESOURCES_TYPE_TV) {
            id = Long.parseLong(playItem.getRadioId());
        } else if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_ALBUM
                || playItem.getType() == PlayerConstants.RESOURCES_TYPE_FEATURE) {
            id = Long.parseLong(playItem.getAlbumId());
        } else if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_RADIO) {
            id = Long.parseLong(playItem.getRadioId());
        }
        return id;
    }

    /**
     * 判断两个PlayItem是否是相同的节目
     *
     * @param first
     * @param second
     * @return
     */
    public boolean isSameProgram(PlayItem first, PlayItem second) {
        if (first instanceof TVPlayItem && second instanceof TVPlayItem) {
            //针对听电视进行判断
            //过滤都是听电视的情况
            //听电视节目的audioId取自接口的programId，永远是0，因此需要匹配listenTvId字段，该字段赋值给了PlayItem的albumId字段。
            return first.getRadioId() != null && second.getRadioId() != null && first.getRadioId().equals(second.getRadioId());
        } else if (first instanceof BroadcastPlayItem && second instanceof BroadcastPlayItem) {
            //针对广播进行判断
            //广播直播的audioId为0，不能只对audioId进行对比
            return first.getRadioId() != null && second.getRadioId() != null && first.getRadioId().equals(second.getRadioId()) && first.getAudioId() == second.getAudioId();
        } else if (first != null && second != null && first.getAudioId() == second.getAudioId()) {
            //如果两个playItem的audioId相同，也有可能是一个广播直播一个听电视，这种情况需要过滤
            if ((first instanceof BroadcastPlayItem && second instanceof TVPlayItem) || (first instanceof TVPlayItem && second instanceof BroadcastPlayItem)) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * 是否播放的相同节目
     *
     * @param id
     * @return
     */
    public boolean isSameProgram(String id) {
        PlayItem curPlayItem = getCurPlayItem();
        return isSameProgram(curPlayItem, id);
    }

    /**
     * 是否播放的相同节目
     *
     * @param id
     * @return
     */
    public boolean isSameProgram(PlayItem playItem, String id) {
        if (playItem == null) return false;
        switch (playItem.getType()) {
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
            case PlayerConstants.RESOURCES_TYPE_TV:
            case PlayerConstants.RESOURCES_TYPE_RADIO:
                return playItem.getRadioId() != null && playItem.getRadioId().equals(String.valueOf(id));
            case PlayerConstants.RESOURCES_TYPE_ALBUM:
            case PlayerConstants.RESOURCES_TYPE_FEATURE:
                return playItem.getAlbumId() != null && playItem.getAlbumId().equals(String.valueOf(id));
            case PlayerConstants.RESOURCES_TYPE_LIVING:
                LivePlayItem livePlayItem = (LivePlayItem) playItem;
                return String.valueOf(livePlayItem.getLiveId()).equals(id);
            default:
                return String.valueOf(playItem.getAudioId()).equals(String.valueOf(id));
        }
    }


    /**
     * 广播或听电视是否不支持回放
     *
     * @return
     */
    public static boolean isBroadcastOrTvNotSupportPlayBack() {
        PlaylistInfo playListInfo = PlayerManager.getInstance().getPlayListInfo();
        if (playListInfo == null) return false;
        if (PlayerManagerHelper.getInstance().isBroadcastPlayer()) {
            return playListInfo.getProgramEnable() != PlayerConstants.BROADCAST_PROGRAM_SHOW;
        }
        return PlayerManagerHelper.getInstance().isTvPlayer();
    }

    public static void startTempTask(TempTaskPlayItem playItem, boolean pausePlayItemState) {
        PlayerManager.getInstance().startTempTask(playItem, pausePlayItemState);
    }

    public static void startTempTask(TempTaskPlayItem playItem) {
        PlayerManager.getInstance().startTempTask(playItem);
    }

    public static void startTempTask(TempTaskPlayItem playItem, VideoView videoView) {
        PlayerManager.getInstance().startTempTask(playItem, videoView);
    }
    
    public PlayItem getCurPlayItem(){
        return mPlayerManager.getCurPlayItem();
    }
}
