package com.kaolafm.kradio.subscribe;

import android.content.Intent;
import android.util.Log;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.ErrorInfo;
import com.kaolafm.kradio.common.ResultCallback;
import com.kaolafm.kradio.lib.bean.SubscribeData;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioSubscribePageLoadInter;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.personalise.model.HotRecommend;
import com.kaolafm.opensdk.api.subscribe.SubscribeInfo;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.http.error.ErrorCode;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.report.event.SubscibeReportEvent;

import java.util.List;

/**
 * <pre>
 *     <AUTHOR> Wenchl
 *     e-mail : <EMAIL>
 *     time   : 2019/03/06
 *     desc   :
 *     version: 1.0
 * </pre>
 */
public class SubscriptionPresenter extends BasePresenter<SubscribeModel, ISubscriptionView> {
    private static final String TAG = "SubscriptionPresenter";
    private static int PAGE_COUNT = 20;

    int firstPageNum = 1;
    public boolean hasNext = false;
    int nextPageNum = -1;
    private int type;

    private SubscriptionUserObserver mSubscriptionUserObserver;

    private KRadioSubscribePageLoadInter kRadioSubscribePageLoadInter;

    public SubscriptionPresenter(ISubscriptionView view, int type) {
        super(view);
        this.type = type;
        mSubscriptionUserObserver = new SubscriptionUserObserver();
        ComponentUtil.addObserver("UserComponent", mSubscriptionUserObserver);
        kRadioSubscribePageLoadInter = ClazzImplUtil.getInter("KRadioSubscribePageLoadImpl");
        if (kRadioSubscribePageLoadInter != null && !kRadioSubscribePageLoadInter.support()) {
            PAGE_COUNT = kRadioSubscribePageLoadInter.getPageCount();
        }
    }

    @Override
    protected SubscribeModel createModel() {
        return new SubscribeModel();
    }

    private void fetchSubscriptionData(int pageIndex) {
        if (mView == null) {
            return;
        }
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)) {
            Log.i(TAG, "fetchSubscriptionData no network");
            mView.showError();
            mView.hideLoading();
            return;
        }
        mModel.getMySubscriptions(type, pageIndex, PAGE_COUNT, new HttpCallback<BasePageResult<List<SubscribeInfo>>>() {
            @Override
            public void onSuccess(BasePageResult<List<SubscribeInfo>> listBasePageResult) {
                //向userfragment 发送订阅成功的消息
//                sendResultToUser(true);
                if (mView != null) {
                    mView.hideLoading();
                    //mView.showSubscriptionData(list);
                    hasNext = listBasePageResult.getHaveNext() == 1;
                    nextPageNum = listBasePageResult.getNextPage();
                    List<SubscribeItemBean> beans = DataConverter.toSubscribeItemBeanFromInfo(listBasePageResult.getDataList());
                    mView.showSubscriptionData(beans, listBasePageResult.getCount(), listBasePageResult.getCurrentPage() != firstPageNum);
                    mView.onShowChange(isShowSubBtn(beans));
                }
            }

            @Override
            public void onError(ApiException e) {
                //向userfragment 发送订阅失败的消息
//                sendResultToUser( false);
                Log.e("SubscriptionPresenter", "onError", e);
                if (mView != null) {
                    mView.hideLoading();
                    if (e.getCode() == ErrorCode.HTTPS_CERTIFICATE_ERROR) {
                        mView.showError(ResUtil.getString(R.string.home_network_certificate_error), true);
                    } else {
                        mView.showError();
                    }
                }
            }
        });
    }

    /**
     * 请求热门推荐内容
     */
    public void getHotRecommend() {
        mModel.getHotRecommend(new HttpCallback<HotRecommend>() {
            @Override
            public void onSuccess(HotRecommend hotRecommend) {
                if (mView != null) {
                    mView.showHotRecommend(hotRecommend);
                }
            }

            @Override
            public void onError(ApiException e) {

            }
        });
    }

    public void fetchFirstPageSubscriptionData() {
        getHotRecommend();
        nextPageNum = -1;
        hasNext = false;
        mView.hideErrorLayout();
        mView.showLoading();
        mView.prepareFragmentStateForShowLoading();
        fetchSubscriptionData(firstPageNum);
    }

    public void fetchNextPageSubscriptionData() {
        if (hasNext) {
            fetchSubscriptionData(nextPageNum);
        }
    }

    /**
     * 判断是否显示订阅按钮。只有当订阅中有专辑时才显示一键播放按钮。
     */
    private boolean isShowSubBtn(List<SubscribeItemBean> list) {
        boolean show = false;
        if (!ListUtil.isEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                SubscribeItemBean subscribeItemBean = list.get(i);
                if (ResType.ALBUM_TYPE == subscribeItemBean.getType()) {
                    show = true;
                    break;
                }
            }
        }
        return show;
    }

    @Override
    public void destroy() {
        super.destroy();
        ComponentUtil.removeObserver("UserComponent", mSubscriptionUserObserver);
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    private class SubscriptionUserObserver implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return getClass().getSimpleName();
        }

        @Override
        public boolean onCall(RealCaller caller) {
            //是否异步返回结果。
//            boolean isAsync;
            String actionName = caller.actionName();
            switch (actionName) {
                case "user_logout":
//                    mView.switchToSubscribeEmptyFragment();
//                    isAsync = false;
                    break;
                case "user_login":
//                    fetchSubscriptionData();
//                    isAsync = true;
                    //只有登录后拉取数据才显示同步。所以只在这里赋值。
                    break;
                default:
//                    isAsync = false;
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }

    private void clearSubscriptions() {
        if (mView != null) {
            mView.clearSubscriptionData();
        }
    }

    public void unsubscribe(SubscribeItemBean subscribeItemBean, int position) {
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)) {
            mView.toast(R.string.no_net_work_str);
            return;
        }
        SubscribeData sd = new SubscribeData();
        sd.setId(subscribeItemBean.getId());
        sd.setName(subscribeItemBean.getName());
        sd.setType(SubscribeHelper.convertSubscriptionType(subscribeItemBean.getType()));
        sd.setImg(subscribeItemBean.getImg());
        sd.setLocation(SubscibeReportEvent.POSITION_SUBSCRIBE_LIST_PAGE);
        sd.setUpdateTime(subscribeItemBean.getUpdateTime());
        //取消订阅
        if (subscribeItemBean.isSubscribed()) {
            mModel.unsubscribe(sd, new ResultCallback() {
                @Override
                public void onResult(boolean result, int code) {
                    subscribeItemBean.setSubscribed(false);
                    if (mView != null && result) {
                        mView.changeSubscribedStatus(position);
                        mView.toast(R.string.un_subscribed_success_str);
                        boolean canSubscribe = PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
                        Intent intent = new Intent().setAction("com.yunting.subscribe");
                        intent.putExtra("canSubscribe", canSubscribe);
                        intent.putExtra("isSubscribe", false);
                        AppDelegate.getInstance().getContext().sendBroadcast(intent);
                    }
                }

                @Override
                public void onFailure(ErrorInfo errorInfo) {

                }
            });
            //订阅
        } else {
            mModel.subscribe(sd, new ResultCallback() {
                @Override
                public void onResult(boolean result, int code) {
                    subscribeItemBean.setSubscribed(true);
                    if (mView != null && result) {
                        mView.changeSubscribedStatus(position);
                        mView.toast(R.string.subscribed_success_str);
                        boolean canSubscribe = PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
                        Intent intent = new Intent().setAction("com.yunting.subscribe");
                        intent.putExtra("canSubscribe", canSubscribe);
                        intent.putExtra("isSubscribe", true);
                        AppDelegate.getInstance().getContext().sendBroadcast(intent);
                    }
                }

                @Override
                public void onFailure(ErrorInfo errorInfo) {
                    Log.e("订阅失败", errorInfo.des);
                }
            });
        }
    }


}
